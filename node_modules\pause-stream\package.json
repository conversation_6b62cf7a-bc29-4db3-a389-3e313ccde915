{"name": "pause-stream", "version": "0.0.11", "description": "a ThroughStream that strictly buffers all readable events when paused.", "main": "index.js", "directories": {"test": "test"}, "devDependencies": {"stream-tester": "0.0.2", "stream-spec": "~0.2.0"}, "scripts": {"test": "node test/index.js && node test/pause-end.js"}, "repository": {"type": "git", "url": "git://github.com/dominictarr/pause-stream.git"}, "keywords": ["stream", "pipe", "pause", "drain", "buffer"], "author": "<PERSON> <<EMAIL>> (dominictarr.com)", "license": ["MIT", "Apache2"], "dependencies": {"through": "~2.3"}}