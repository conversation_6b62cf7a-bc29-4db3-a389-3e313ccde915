const MangaContentDownloader = require('./download-manga-content');

async function testNewLogic() {
    console.log('🧪 开始测试新的下载逻辑...');
    
    const downloader = new MangaContentDownloader();
    
    try {
        await downloader.init();
        
        // 测试参数 - 请根据实际情况修改
        const testMangaId = 'ap101511'; // 替换为实际的漫画ID
        const testMangaName = '测试漫画';
        const testChapter = 1;
        
        console.log(`📖 测试下载: ${testMangaName} 第${testChapter}章`);
        
        const success = await downloader.downloadMangaContent(testMangaId, testMangaName, testChapter);
        
        if (success) {
            console.log('✅ 新下载逻辑测试成功！');
            console.log('📋 测试验证的功能:');
            console.log('  ✓ 智能章节检测');
            console.log('  ✓ 模拟人为滚动');
            console.log('  ✓ 智能图片加载等待');
            console.log('  ✓ 并行图片保存');
            console.log('  ✓ 下载完整性验证');
        } else {
            console.log('❌ 新下载逻辑测试失败！');
            console.log('可能的原因:');
            console.log('  - 章节不存在');
            console.log('  - 网络连接问题');
            console.log('  - 页面结构变化');
        }
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error.message);
        console.log('🔍 错误详情:', error);
    } finally {
        await downloader.close();
        console.log('🔒 测试完成，浏览器已关闭');
    }
}

// 运行测试
if (require.main === module) {
    testNewLogic().catch(console.error);
}

module.exports = testNewLogic;
