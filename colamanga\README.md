# Colamanga 漫画爬虫

基于 Playwright 的 Colamanga 网站漫画爬虫工具，支持自动收集漫画ID和下载漫画内容。

## 功能特性

- 🔍 **自动收集漫画ID**: 爬取 Colamanga 网站的所有漫画信息
- 📥 **批量下载漫画**: 支持批量下载漫画章节内容
- 🖼️ **图片处理**: 自动处理 blob 加密的图片资源
- 📁 **智能组织**: 按漫画名称和章节自动组织文件结构
- 🔄 **断点续传**: 支持断点续传和重试机制
- ⚡ **并发控制**: 合理的延时和错误处理机制

## 安装依赖

项目使用根目录的 package.json，确保已安装 Playwright：

```bash
# 如果还未安装依赖
npm install

# 安装 Playwright 浏览器
npx playwright install chromium
```

## 使用方法

### 1. 快速开始

```bash
# 进入项目目录
cd js_scripts/colamanga

# 执行完整流程（收集ID + 下载前5个漫画）
node run-colamanga.js full --count 5
```

### 2. 分步执行

#### 收集漫画ID
```bash
# 收集所有漫画的ID和名称
node run-colamanga.js collect
```

#### 查看漫画列表
```bash
# 显示已收集的漫画列表
node run-colamanga.js list
```

#### 下载漫画内容
```bash
# 下载所有漫画
node run-colamanga.js download

# 下载前5个漫画
node run-colamanga.js download --start 0 --count 5

# 从第10个开始下载3个漫画
node run-colamanga.js download --start 10 --count 3
```

#### 下载单个漫画
```bash
# 下载指定漫画的第1章
node run-colamanga.js download --id ap101511 --name "漫画名称" --chapter 1
```

### 3. 直接运行脚本

```bash
# 直接运行ID收集器
node collect-manga-ids.js

# 直接运行内容下载器
node download-manga-content.js
```

## 输出结构

```
/Users/<USER>/Documents/manga/
├── manga-ids.json          # 漫画ID列表
├── 漫画名称1/
│   ├── 1-章节标题/
│   │   ├── 1-uuid.png
│   │   ├── 2-uuid.png
│   │   └── ...
│   ├── 2-章节标题/
│   │   └── ...
│   └── 3/                  # 无标题时只显示章节号
│       └── ...
├── 漫画名称2/
│   └── ...
└── ...
```

## 文件说明

- `collect-manga-ids.js`: 漫画ID收集器，爬取所有漫画的基本信息
- `download-manga-content.js`: 漫画内容下载器，下载具体的漫画图片
- `run-colamanga.js`: 统一的运行脚本，提供命令行界面
- `README.md`: 使用说明文档

## 技术特点

### 网站结构理解
- **漫画结构**: 漫画 → 章节 → 页面 → 图片
- **URL格式**: `/manga-{ID}/{章节号}/{页面号}.html`
- **示例**: `/manga-ap101511/1/1.html` (第1章第1页)
- **一个章节包含多个页面，每个页面包含多张图片**

### 图片处理
- 使用 Playwright 截图功能获取 blob 图片
- 自动处理加密的图片资源
- 直接从页面元素获取图片数据

### 页面滚动加载
- 自动滚动页面确保所有内容加载
- 智能等待机制避免遗漏内容

### 文件命名规则
- 章节目录格式：`{章节号}-{章节标题}` 或 `{章节号}`（无标题时）
- 图片文件名格式：`{页面内顺序}-{blob-uuid}.png`
- 自动从 blob URL 提取 UUID
- 按页面中的 `p` 属性值排序

### 错误处理
- 完善的错误处理和重试机制
- 自动跳过已存在的文件
- 404错误自动判断章节结束

## 注意事项

1. **合法使用**: 请确保遵守网站的使用条款和相关法律法规
2. **访问频率**: 脚本已内置延时机制，避免对服务器造成过大压力
3. **存储空间**: 漫画图片较大，请确保有足够的存储空间
4. **网络环境**: 建议在稳定的网络环境下运行

## 断点续传详细说明

### 工作原理

1. **进度分析**: 每次下载前自动分析已下载的文件
   - 识别已存在的页面
   - 检测缺失的页面
   - 判断章节是否完整

2. **智能策略**:
   - ✅ **完整章节**: 直接跳过，显示"已完整下载，跳过！"
   - 🔄 **缺失页面**: 优先下载缺失的页面
   - 🔍 **新内容**: 从最大页码+1开始探索新页面

3. **示例场景**:
```bash
# 第一次运行 - 下载了1-10页
node run-colamanga.js download --id ap101511 --name "示例漫画" --chapter 1

# 网络中断，只下载了1,2,4,5,7-10页，缺失3,6页
# 第二次运行 - 自动检测并优先下载缺失页面
node run-colamanga.js download --id ap101511 --name "示例漫画" --chapter 1

输出:
📊 下载进度分析:
   - 已下载: 8 张图片
   - 最大页码: 10
   - 缺失页面: 3, 6
   - 是否完整: ❌

🔄 优先下载缺失页面: 3, 6
📄 下载缺失页面: 第 3 页
✅ 第 3 页下载完成，共 1 张图片
📄 下载缺失页面: 第 6 页
✅ 第 6 页下载完成，共 1 张图片

🔍 从第 11 页开始探索新内容
📄 正在下载第 11 页
✅ 第 11 页下载完成，共 1 张图片
```

### 使用断点续传示例

```bash
# 运行断点续传示例
node example-resume-download.js
```

## 常见问题

### Q: 下载失败怎么办？
A: 脚本支持智能断点续传：
- 重新运行相同命令即可自动继续
- 会自动检测缺失页面并优先下载
- 已下载的文件不会重复下载

### Q: 如何修改下载路径？
A: 修改脚本中的 `outputDir` 变量即可。

### Q: 如何调整下载速度？
A: 修改脚本中的 `waitForTimeout` 延时时间。

### Q: 浏览器无法启动？
A: 运行 `npx playwright install chromium` 安装浏览器。

### Q: 断点续传不工作？
A: 请确保：
- 不要手动修改下载的文件名
- 文件名格式必须是 `{页码}-{uuid}.png`
- 目录结构保持不变

### Q: 看到"正在下载第 2 页"但没有第2章目录？
A: 这是正常的！请注意区分：
- **页面（Page）**: 一个章节内的页面，如第1页、第2页...
- **章节（Chapter）**: 不同的章节，如第1章、第2章...
- 日志中的"第 X 页"指的是页面，所有页面都保存在同一个章节目录下
- 只有当漫画有多个章节时，才会创建多个章节目录

## 许可证

MIT License
