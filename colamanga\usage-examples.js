const MangaContentDownloader = require('./download-manga-content');
const path = require('path');
const fs = require('fs-extra');

// 示例1: 下载单个章节
async function downloadSingleChapter() {
    const downloader = new MangaContentDownloader();
    
    try {
        await downloader.init();
        
        // 下载指定漫画的指定章节
        const mangaId = 'your-manga-id';  // 替换为实际的漫画ID
        const mangaName = '漫画名称';
        const chapter = 1;
        
        const success = await downloader.downloadMangaContent(mangaId, mangaName, chapter);
        
        if (success) {
            console.log(`✅ 章节 ${chapter} 下载成功`);
        } else {
            console.log(`❌ 章节 ${chapter} 下载失败`);
        }
        
    } catch (error) {
        console.error('下载出错:', error);
    } finally {
        await downloader.close();
    }
}

// 示例2: 批量下载多个章节
async function downloadMultipleChapters() {
    const downloader = new MangaContentDownloader();
    
    try {
        await downloader.init();
        
        const mangaId = 'your-manga-id';  // 替换为实际的漫画ID
        const mangaName = '漫画名称';
        const startChapter = 1;
        const endChapter = 5;
        
        for (let chapter = startChapter; chapter <= endChapter; chapter++) {
            console.log(`\n📚 开始下载第 ${chapter} 章...`);
            
            const success = await downloader.downloadMangaContent(mangaId, mangaName, chapter);
            
            if (success) {
                console.log(`✅ 第 ${chapter} 章下载完成`);
            } else {
                console.log(`❌ 第 ${chapter} 章下载失败，可能章节不存在`);
                break; // 如果章节不存在，停止下载后续章节
            }
            
            // 章节间延迟，避免请求过快
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
    } catch (error) {
        console.error('批量下载出错:', error);
    } finally {
        await downloader.close();
    }
}

// 示例3: 从漫画列表文件批量下载
async function downloadFromList() {
    const downloader = new MangaContentDownloader();
    
    try {
        await downloader.init();
        
        const mangaListFile = path.join(__dirname, 'manga-ids.json');
        
        if (await fs.pathExists(mangaListFile)) {
            // 下载前3个漫画，每个漫画最多下载5章
            await downloader.downloadFromMangaList(mangaListFile, 0, 3, 5);
        } else {
            console.log('❌ 未找到漫画列表文件: manga-ids.json');
            console.log('请先运行 collect-manga-ids.js 生成漫画列表');
        }
        
    } catch (error) {
        console.error('从列表下载出错:', error);
    } finally {
        await downloader.close();
    }
}

// 示例4: 检查章节是否存在（不下载）
async function checkChapterExists() {
    const downloader = new MangaContentDownloader();
    
    try {
        await downloader.init();
        
        const mangaId = 'your-manga-id';  // 替换为实际的漫画ID
        const chapter = 1;
        
        const exists = await downloader.checkChapterExists(mangaId, chapter);
        
        if (exists) {
            console.log(`✅ 章节 ${chapter} 存在`);
        } else {
            console.log(`❌ 章节 ${chapter} 不存在`);
        }
        
    } catch (error) {
        console.error('检查章节出错:', error);
    } finally {
        await downloader.close();
    }
}

// 示例5: 分析本地章节进度
async function analyzeLocalProgress() {
    const downloader = new MangaContentDownloader();
    
    const mangaName = '漫画名称';
    const chapterName = '第1章-章节标题';
    const chapterDir = path.join(downloader.outputDir, downloader.sanitizeFileName(mangaName), chapterName);
    
    const progress = await downloader.analyzeChapterProgress(chapterDir);
    
    console.log('📊 本地章节分析结果:');
    console.log(`  总文件数: ${progress.totalExisting}`);
    console.log(`  最大页码: ${progress.maxPage}`);
    console.log(`  缺失页面: ${progress.missingPages.join(', ') || '无'}`);
    console.log(`  是否完整: ${progress.isComplete ? '是' : '否'}`);
}

// 根据需要运行不同的示例
async function main() {
    console.log('🚀 漫画下载器使用示例');
    console.log('请根据需要修改代码中的漫画ID和名称');
    
    // 取消注释需要运行的示例
    
    // await downloadSingleChapter();
    // await downloadMultipleChapters();
    // await downloadFromList();
    // await checkChapterExists();
    // await analyzeLocalProgress();
    
    console.log('💡 提示: 请修改示例中的漫画ID和名称后运行');
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    downloadSingleChapter,
    downloadMultipleChapters,
    downloadFromList,
    checkChapterExists,
    analyzeLocalProgress
};
