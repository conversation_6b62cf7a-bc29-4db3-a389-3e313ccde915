{"name": "bowser", "version": "1.9.4", "description": "Lightweight browser detector", "keywords": ["browser", "useragent", "user-agent", "parser", "ua", "detection", "ender", "sniff"], "homepage": "https://github.com/lancedikson/bowser", "author": "<PERSON> <<EMAIL>> (http://dustindiaz.com)", "main": "./src/bowser.js", "typings": "./typings.d.ts", "repository": {"type": "git", "url": "git+https://github.com/ded/bowser.git"}, "devDependencies": {"smoosh": "*", "mocha": "*"}, "bugs": {"url": "https://github.com/ded/bowser/issues"}, "directories": {"test": "test"}, "scripts": {"test": "make test", "prepublish": "make boosh"}, "license": "MIT"}