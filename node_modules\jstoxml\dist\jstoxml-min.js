!function(t,e){if("function"==typeof define&&define.amd)define(["exports"],e);else if("undefined"!=typeof exports)e(exports);else{var n={exports:{}};e(n.exports),t.jstoxml=n.exports}}("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:this,function(t){"use strict";function e(t){return function(t){if(Array.isArray(t))return n(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return n(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function o(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach(function(e){a(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function a(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function c(t){"@babel/helpers - typeof";return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Object.defineProperty(t,"__esModule",{value:!0}),t.toXML=t.default=void 0;var i=["_selfCloseTag","_attrs"],u=new RegExp(i.join("|"),"g"),f=function(t){return(Array.isArray(t)?"array":"object"===c(t)&&null!==t&&t._name&&"special-object")||t instanceof Date&&"date"||null===t&&"null"||c(t)},l=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("string"!=typeof t)return t;var n=new RegExp("(".concat(Object.keys(e).join("|"),")(?!(\\w|#)*;)"),"g");return String(t).replace(n,function(t,n){return e[n]||""})},s=function(){var t=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0,n=[];n=Array.isArray(t)?t.map(function(t){var n=Object.keys(t)[0],r=t[n],o=e?l(r,e):r,a=!0===o?"":'="'.concat(o,'"');return"".concat(n).concat(a)}):Object.keys(t).map(function(n){var r=e?l(t[n],e):t[n],o=!0===t[n]?"":'="'.concat(r,'"');return"".concat(n).concat(o)});return n}(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},arguments.length>1?arguments[1]:void 0);if(0===t.length)return"";var e=t.join(" ");return" ".concat(e)},p=["string","number","boolean"],b=function(t){return p.includes(f(t))},y=([].concat(p,["date","special-object"]),{"<":"&lt;",">":"&gt;","&":"&amp;"}),d=function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=r.depth,p=void 0===a?0:a,d=r.indent,v=r._isFirstItem,m=r._isOutputStart,g=void 0===m||m,h=r.header,j=r.attributesFilter,O=void 0===j?{}:j,_=r.filter,S=void 0===_?{}:_,k="boolean"==typeof O&&!O?{}:o(o(o({},y),{'"':"&quot;"}),O),w="boolean"==typeof S&&!S?{}:o(o({},y),S),A=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return t.repeat(e)}(d,p),x=f(n),P=function(t){var e=t.header,n=(t.indent,t.isOutputStart);return e&&n?"boolean"===c(e)?'<?xml version="1.0" encoding="UTF-8"?>':e:""}({header:h,indent:d,depth:p,isOutputStart:g}),I=g&&!P&&v&&0===p,T="";switch(x){case"special-object":var E=n._name,D=n._content;if(null===D){T=E;break}if(Array.isArray(D)&&D.every(b))return D.map(function(e){return t({_name:E,_content:e},o(o({},r),{},{depth:p,_isOutputStart:!1}))}).join("");if(E.match(u))break;var F=t(D,o(o({},r),{},{depth:p+1,_isOutputStart:I})),L=f(F),M=!F.match("<"),C="".concat(d&&!I?"\n":"").concat(A);if("_comment"===E){T+="".concat(C,"\x3c!-- ").concat(D," --\x3e");break}var X="undefined"===L||""===F,R="boolean"===c(n._selfCloseTag)?X&&n._selfCloseTag:X,U=R?"/":"",q=s(n._attrs,k),$="<".concat(E).concat(q).concat(U,">"),z=d&&!M?"\n".concat(A):"",B=R?"":"".concat(F).concat(z,"</").concat(E,">");T+="".concat(C).concat($).concat(B);break;case"object":var G=Object.keys(n);T=G.map(function(a,c){var u=o(o({},r),{},{_isFirstItem:0===c,_isLastItem:c+1===G.length,_isOutputStart:I}),l={_name:a};if("object"===f(n[a])&&(i.forEach(function(t){var e=n[a][t];void 0!==e&&(l[t]=e,delete n[a][t])}),void 0!==n[a]._content&&Object.keys(n[a]).length>1)){var s=Object.assign({},n[a]);delete s._content,l._content=[].concat(e(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(t).map(function(e){return{_name:e,_content:t[e]}})}(s)),[n[a]._content])}return void 0===l._content&&(l._content=n[a]),t(l,u,a)},r).join("");break;case"function":var H=n(r);T=t(H,r);break;case"array":T=n.map(function(e,a){var c=o(o({},r),{},{_isFirstItem:0===a,_isLastItem:a+1===n.length,_isOutputStart:I});return t(e,c)}).join("");break;default:T=l(n,w)}return"".concat(P).concat(T)};t.toXML=d;var v={toXML:d};t.default=v});