{"name": "get-ready", "version": "1.0.0", "description": "mixin to add one-time ready event callback handler", "main": "index.js", "files": ["index.js"], "dependencies": {}, "devDependencies": {"eslint": "1", "istanbul": "0", "mocha": "2", "should": "7", "thunk-mocha": "0"}, "scripts": {"lint": "eslint index.js test", "test": "mocha -r thunk-mocha test/*.test.js", "test-cov": "istanbul cover node_modules/.bin/_mocha -- -r thunk-mocha test/*.test.js"}, "repository": {"type": "git", "url": "git://github.com/node-modules/ready"}, "keywords": ["ready", "once", "event"], "author": "fengmk2 <<EMAIL>> (http://fengmk2.com)", "license": "MIT", "bugs": {"url": "https://github.com/node-modules/ready/issues"}}