{"name": "end-or-error", "version": "1.0.1", "description": "Listen readable stream `end` or `error` event once", "main": "index.js", "files": ["index.js"], "scripts": {"test": "mocha --check-leaks -R spec -t 5000 test/*.test.js", "test-cov": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha -- --check-leaks -t 5000 test/*.test.js", "test-travis": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha --report lcovonly -- --check-leaks -t 5000 test/*.test.js", "jshint": "jshint .", "autod": "autod -w --prefix '~'", "cnpm": "npm install --registry=https://registry.npm.taobao.org", "contributors": "contributors -f plain -o AUTHORS"}, "dependencies": {}, "devDependencies": {"autod": "*", "contributors": "*", "jshint": "*", "istanbul": "*", "mocha": "*"}, "homepage": "https://github.com/stream-utils/end-or-error", "repository": {"type": "git", "url": "git://github.com/stream-utils/end-or-error.git", "web": "https://github.com/stream-utils/end-or-error"}, "bugs": {"url": "https://github.com/stream-utils/end-or-error/issues", "email": "<EMAIL>"}, "keywords": ["end-or-error", "readstream", "readable", "stream", "end", "eoe", "stream-utils"], "engines": {"node": ">= 0.11.14"}, "author": "fengmk2 <<EMAIL>> (http://fengmk2.com)", "license": "MIT"}