const { chromium } = require('playwright');
const fs = require('fs-extra');
const path = require('path');

class MangaContentDownloader {
    constructor() {
        this.browser = null;
        this.page = null;
        this.outputDir = 'E:\\manga';
    }

    async init() {
        console.log('🚀 启动浏览器...');
        
        // 方案1: 使用普通启动 + 扩展
        this.browser = await chromium.launch({
            headless: false,
            channel: 'chrome',
            args: [
                // '--load-extension=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Extensions\\',
                '--no-sandbox',
                '--disable-setuid-sandbox'
            ],
            timeout: 300000
        });
        
        this.context = await this.browser.newContext();
        this.page = await this.context.newPage();
        
        // 设置用户代理
        await this.page.setExtraHTTPHeaders({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        });

        // 监听浏览器控制台消息
        this.page.on('console', msg => {
            // console.log(`🖥️ 浏览器控制台: ${msg.text()}`);
        });
        
        // 确保输出目录存在
        await fs.ensureDir(this.outputDir);

        // 设置 blob 图片捕获
        await this.setupBlobCapture();
    }

    async setupBlobCapture() {
        // 简化的blob URL监听，仅用于调试
        await this.page.addInitScript(() => {
            const originalCreateObjectURL = URL.createObjectURL;
            URL.createObjectURL = function(object) {
                const blobUrl = originalCreateObjectURL.call(this, object);
                // 将 blob URL 信息传递给页面，用于调试
                window.__blobUrls = window.__blobUrls || [];
                window.__blobUrls.push({
                    blobUrl: blobUrl,
                    size: object.size,
                    type: object.type,
                    timestamp: Date.now()
                });
                console.log('🔗 创建blob URL:', blobUrl, 'size:', object.size);
                return blobUrl;
            };
        });
    }

    async getChapterTitle() {
        try {
            return await this.page.evaluate(() => {
                const titleElement = document.querySelector('.mh_readtitle');
                console.log('🔍 查找标题元素:', titleElement ? '找到' : '未找到');

                if (titleElement) {
                    let title = titleElement.textContent.trim();
                    console.log('📝 原始标题:', title);

                    // 清理标题，移除导航文本
                    title = title.replace(/返回目录/g, '');
                    title = title.replace(/返回首页/g, '');
                    title = title.replace(/上一章/g, '');
                    title = title.replace(/下一章/g, '');
                    title = title.replace(/\s+/g, ' '); // 合并多个空格
                    title = title.trim();

                    console.log('🧹 清理后标题:', title);
                    return title || null;
                }
                return null;
            });
        } catch (error) {
            console.log('⚠️ 无法获取章节标题:', error.message);
            return null;
        }
    }

    async downloadMangaContent(mangaId, mangaName, chapter = 1) {
        console.log(`📖 开始下载漫画: ${mangaName} (ID: ${mangaId}), 章节: ${chapter}`);

        // 创建漫画目录
        const mangaDir = path.join(this.outputDir, this.sanitizeFileName(mangaName));
        await fs.ensureDir(mangaDir);

        // 访问章节页面获取章节标题
        const chapterUrl = `https://www.colamanga.com/manga-${mangaId}/1/${chapter}.html`;
        console.log(`🔗 访问章节: ${chapterUrl}`);

        try {
            await this.page.goto(chapterUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
        } catch (error) {
            if (error.message.includes('404') || error.message.includes('net::ERR_HTTP_RESPONSE_CODE_FAILURE')) {
                console.log(`📄 章节 ${chapter} 不存在`);
                return false; // 返回false表示章节不存在
            }
            throw error;
        }

        // 获取章节标题
        const chapterTitle = await this.getChapterTitle();
        console.log(`📝 章节标题: ${chapterTitle || '未获取到标题'}`);

        // 确保每个章节都有唯一的目录名，即使标题相同或为空
        const chapterDirName = chapterTitle ?
            `第${chapter}章-${this.sanitizeFileName(chapterTitle)}` :
            `第${chapter}章`;

        const chapterDir = path.join(mangaDir, chapterDirName);
        await fs.ensureDir(chapterDir);

        console.log(`📁 章节目录: ${chapterDirName}`);
        console.log(`📂 完整路径: ${chapterDir}`);

        // 智能检测章节完整性
        const chapterStatus = await this.analyzeChapterCompleteness(chapterDir);

        if (chapterStatus.isComplete) {
            console.log(`✅ 章节已完整下载，跳过重复下载`);
            return true;
        } else if (chapterStatus.hasPartialContent) {
            console.log(`📊 发现部分内容，将进行增量下载`);
            return await this.performIncrementalDownload(chapterDir, chapterStatus);
        } else {
            console.log(`🆕 开始全新下载章节`);
            return await this.performFullDownload(chapterDir);
        }
    }

    async analyzeChapterCompleteness(chapterDir) {
        // 分析章节完整性
        const localProgress = await this.analyzeChapterProgress(chapterDir);

        if (localProgress.totalExisting === 0) {
            return { isComplete: false, hasPartialContent: false, localProgress };
        }

        // 获取网页中的图片数量
        const webImageCount = await this.getWebImageCount();

        console.log(`📊 图片数量对比: 本地 ${localProgress.totalExisting} 张 vs 网页 ${webImageCount} 张`);

        if (localProgress.totalExisting === webImageCount && webImageCount > 0 && localProgress.missingPages.length === 0) {
            return { isComplete: true, hasPartialContent: false, localProgress, webImageCount };
        } else if (localProgress.totalExisting > 0) {
            return { isComplete: false, hasPartialContent: true, localProgress, webImageCount };
        } else {
            return { isComplete: false, hasPartialContent: false, localProgress, webImageCount };
        }
    }

    async getWebImageCount() {
        try {
            // 等待页面内容加载
            await this.page.waitForSelector('.mh_comicpic', { timeout: 15000 });

            // 使用优化的滚动策略
            await this.humanLikeScroll();

            // 获取网页中带有p属性的.mh_comicpic元素数量
            const webImageCount = await this.page.evaluate(() => {
                const comicPics = document.querySelectorAll('.mh_comicpic');
                let validElementCount = 0;

                for (let i = 0; i < comicPics.length; i++) {
                    const pic = comicPics[i];
                    const pValue = pic.getAttribute('p');

                    if (pValue) {
                        validElementCount++;
                    }
                }

                console.log(`🔍 网页中找到 ${validElementCount} 个带p属性的.mh_comicpic元素`);
                return validElementCount;
            });

            return webImageCount;
        } catch (error) {
            console.log(`⚠️ 获取网页图片数量时出错: ${error.message}`);
            return 0;
        }
    }

    async humanLikeScroll() {
        // 模拟人为的滚动行为
        console.log(`🖱️ 开始模拟人为滚动...`);

        await this.page.evaluate(async () => {
            await new Promise((resolve) => {
                let totalHeight = 0;
                const distance = 150; // 较小的滚动距离，更像人为操作
                const scrollHeight = document.body.scrollHeight;

                const timer = setInterval(() => {
                    window.scrollBy(0, distance);
                    totalHeight += distance;

                    if (totalHeight >= scrollHeight) {
                        clearInterval(timer);
                        // 滚动到顶部，然后再慢慢滚动到底部
                        window.scrollTo(0, 0);
                        setTimeout(() => {
                            let currentHeight = 0;
                            const slowTimer = setInterval(() => {
                                window.scrollBy(0, 100);
                                currentHeight += 100;
                                if (currentHeight >= scrollHeight) {
                                    clearInterval(slowTimer);
                                    setTimeout(resolve, 3000); // 最后等待3秒确保加载
                                }
                            }, 500); // 更慢的滚动速度
                        }, 1000);
                    }
                }, 800); // 增加滚动间隔，模拟人为操作
            });
        });

        console.log(`✅ 滚动完成`);
    }

    async performIncrementalDownload(chapterDir, chapterStatus) {
        console.log(`🔄 开始增量下载，缺失页面: ${chapterStatus.localProgress.missingPages.join(', ')}`);

        // 等待图片加载完成
        await this.waitForImagesLoaded();

        // 只下载缺失的图片
        const downloadedCount = await this.downloadMissingImages(chapterDir, chapterStatus.localProgress.missingPages);

        if (downloadedCount > 0) {
            console.log(`✅ 增量下载完成，新下载 ${downloadedCount} 张图片`);

            // 验证下载完整性
            return await this.verifyAndRetryIfNeeded(chapterDir);
        } else {
            console.log(`⚠️ 增量下载未找到新图片`);
            return false;
        }
    }

    async performFullDownload(chapterDir) {
        console.log(`🆕 开始完整下载章节`);

        // 等待页面内容加载
        try {
            await this.page.waitForSelector('.mh_comicpic', { timeout: 15000 });
        } catch (error) {
            console.log(`⚠️ 没有找到图片内容`);
            return false;
        }

        // 等待图片加载完成
        await this.waitForImagesLoaded();

        // 下载所有图片
        const downloadedCount = await this.downloadPageImages(chapterDir);

        if (downloadedCount > 0) {
            console.log(`✅ 完整下载完成，共 ${downloadedCount} 张图片`);

            // 验证下载完整性
            return await this.verifyAndRetryIfNeeded(chapterDir);
        } else {
            console.log(`⚠️ 完整下载未找到图片`);
            return false;
        }
    }

    async waitForImagesLoaded() {
        console.log(`⏳ 等待图片加载完成...`);

        let attempts = 0;
        const maxAttempts = 10;

        while (attempts < maxAttempts) {
            const loadedCount = await this.page.evaluate(() => {
                const comicPics = document.querySelectorAll('.mh_comicpic');
                let loadedCount = 0;

                for (let i = 0; i < comicPics.length; i++) {
                    const pic = comicPics[i];
                    const img = pic.querySelector('img');
                    const pValue = pic.getAttribute('p');

                    if (img && pValue && img.src && img.src.startsWith('blob:')) {
                        loadedCount++;
                    }
                }

                console.log(`📊 已加载 ${loadedCount}/${comicPics.length} 张图片`);
                return { loaded: loadedCount, total: comicPics.length };
            });

            if (loadedCount.loaded === loadedCount.total && loadedCount.total > 0) {
                console.log(`✅ 所有图片加载完成 (${loadedCount.loaded}/${loadedCount.total})`);
                break;
            }

            console.log(`⏳ 图片加载中... (${loadedCount.loaded}/${loadedCount.total})`);
            await new Promise(resolve => setTimeout(resolve, 2000));
            attempts++;
        }

        if (attempts >= maxAttempts) {
            console.log(`⚠️ 等待图片加载超时，继续执行下载`);
        }
    }

    async downloadMissingImages(chapterDir, missingPages) {
        console.log(`🔍 开始下载缺失的图片，页面: ${missingPages.join(', ')}`);

        // 获取页面上的所有图片元素信息
        const imageInfos = await this.page.evaluate((targetPages) => {
            const comicPics = document.querySelectorAll('.mh_comicpic');
            const images = [];

            console.log(`🔍 找到 ${comicPics.length} 个 .mh_comicpic 元素`);

            for (let i = 0; i < comicPics.length; i++) {
                const pic = comicPics[i];
                const img = pic.querySelector('img');
                const pValue = pic.getAttribute('p');

                if (img && pValue) {
                    const pageNum = parseInt(pValue);
                    if (targetPages.includes(pageNum)) {
                        const src = img.src;
                        if (src && src.startsWith('blob:')) {
                            images.push({
                                blobUrl: src,
                                order: pageNum
                            });
                            console.log(`✅ 找到缺失页面的blob图片: ${src}, p=${pValue}`);
                        } else {
                            console.log(`⚠️ 页面 ${pageNum} 的图片未加载或非blob URL: ${src}`);
                        }
                    }
                }
            }

            console.log(`📊 找到 ${images.length} 张缺失的blob图片`);
            return images.sort((a, b) => a.order - b.order);
        }, missingPages);

        return await this.saveImages(imageInfos, chapterDir);
    }

    async verifyAndRetryIfNeeded(chapterDir, maxRetries = 2) {
        console.log(`🔍 验证下载完整性...`);

        for (let retry = 0; retry < maxRetries; retry++) {
            const chapterStatus = await this.analyzeChapterCompleteness(chapterDir);

            if (chapterStatus.isComplete) {
                console.log(`✅ 章节下载完整，验证通过`);
                return true;
            }

            if (chapterStatus.hasPartialContent && chapterStatus.localProgress.missingPages.length > 0) {
                console.log(`⚠️ 发现缺失页面: ${chapterStatus.localProgress.missingPages.join(', ')}, 尝试重试 ${retry + 1}/${maxRetries}`);

                // 刷新页面
                await this.page.reload({ waitUntil: 'domcontentloaded' });
                await new Promise(resolve => setTimeout(resolve, 3000));

                // 重新加载图片
                await this.humanLikeScroll();
                await this.waitForImagesLoaded();

                // 下载缺失的图片
                const downloadedCount = await this.downloadMissingImages(chapterDir, chapterStatus.localProgress.missingPages);
                console.log(`🔄 重试下载了 ${downloadedCount} 张图片`);
            } else {
                console.log(`❌ 章节下载失败，无法修复`);
                return false;
            }
        }

        console.log(`⚠️ 达到最大重试次数，章节可能不完整`);
        return false;
    }

    async fastScrollToLoadElements() {
        // 快速滚动页面以确保所有DOM元素加载（不等待图片）
        await this.page.evaluate(async () => {
            await new Promise((resolve) => {
                let totalHeight = 0;
                const distance = 500; // 更大的滚动距离
                const timer = setInterval(() => {
                    const scrollHeight = document.body.scrollHeight;
                    window.scrollBy(0, distance);
                    totalHeight += distance;

                    if (totalHeight >= scrollHeight) {
                        clearInterval(timer);
                        // 只需要短暂等待DOM元素加载，不等待图片
                        setTimeout(resolve, 500);
                    }
                }, 100); // 更快的滚动间隔
            });
        });
    }

    async downloadPageImages(chapterDir) {
        // 获取页面上的所有图片元素信息
        const imageInfos = await this.page.evaluate(() => {
            const comicPics = document.querySelectorAll('.mh_comicpic');
            const images = [];

            console.log(`🔍 找到 ${comicPics.length} 个 .mh_comicpic 元素`);

            for (let i = 0; i < comicPics.length; i++) {
                const pic = comicPics[i];
                const img = pic.querySelector('img');
                const pValue = pic.getAttribute('p');

                if (img && pValue) {
                    const src = img.src;
                    if (src && src.startsWith('blob:')) {
                        images.push({
                            blobUrl: src,
                            order: parseInt(pValue) || 0
                        });
                        console.log(`✅ 找到blob图片: ${src}, p=${pValue}`);
                    } else {
                        console.log(`⚠️ 非blob URL: ${src}`);
                    }
                }
            }

            console.log(`📊 总共找到 ${images.length} 张blob图片`);
            return images.sort((a, b) => a.order - b.order);
        });

        console.log(`🖼️ 找到 ${imageInfos.length} 张图片`);
        return await this.saveImages(imageInfos, chapterDir);
    }

    async saveImages(imageInfos, chapterDir) {
        console.log(`💾 开始并行保存 ${imageInfos.length} 张图片...`);

        // 并行下载，但限制并发数量避免过载
        const concurrency = 3; // 同时下载3张图片
        let downloadedCount = 0;

        for (let i = 0; i < imageInfos.length; i += concurrency) {
            const batch = imageInfos.slice(i, i + concurrency);

            const promises = batch.map(async (imageInfo) => {
                try {
                    // 从blob URL中提取UUID
                    const uuid = this.extractUuidFromBlob(imageInfo.blobUrl);
                    const fileName = `${imageInfo.order}-${uuid}.png`;
                    const filePath = path.join(chapterDir, fileName);

                    // 检查文件是否已存在
                    if (await fs.pathExists(filePath)) {
                        console.log(`⏭️ 文件已存在，跳过: ${fileName}`);
                        return true;
                    }

                    // 使用Playwright的页面截图功能来获取图片
                    const imgSelector = `.mh_comicpic[p="${imageInfo.order}"] img`;

                    const imgElement = await this.page.$(imgSelector);
                    if (imgElement) {
                        console.log(`📸 截图元素: p=${imageInfo.order}`);
                        const buffer = await imgElement.screenshot();

                        await fs.writeFile(filePath, buffer);
                        console.log(`💾 保存图片: ${fileName} (${buffer.length} bytes)`);
                        return true;
                    } else {
                        console.error(`❌ 未找到元素: ${imgSelector}`);
                        return false;
                    }
                } catch (error) {
                    console.error(`❌ 保存图片失败 (p=${imageInfo.order}):`, error.message);
                    return false;
                }
            });

            const results = await Promise.all(promises);
            downloadedCount += results.filter(result => result).length;

            // 批次间短暂延迟
            if (i + concurrency < imageInfos.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        console.log(`✅ 并行保存完成，成功保存 ${downloadedCount}/${imageInfos.length} 张图片`);
        return downloadedCount;
    }

    extractUuidFromBlob(blobUrl) {
        // 从blob URL中提取UUID
        // 格式: blob:https://www.colamanga.com/91799778-e7d0-401c-ba8c-5d9b02672782
        const match = blobUrl.match(/([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/);
        return match ? match[1] : 'unknown';
    }

    async getExistingImages(chapterDir) {
        // 获取已存在的图片文件
        if (!await fs.pathExists(chapterDir)) {
            return [];
        }

        const files = await fs.readdir(chapterDir);
        return files.filter(f => f.endsWith('.png') || f.endsWith('.jpg') || f.endsWith('.jpeg'));
    }

    async clearChapterDirectory(chapterDir) {
        try {
            console.log(`🗑️ 清空章节目录: ${chapterDir}`);
            await fs.emptyDir(chapterDir);
            console.log(`✅ 章节目录已清空`);
        } catch (error) {
            console.error(`❌ 清空目录失败: ${error.message}`);
        }
    }

    async analyzeChapterProgress(chapterDir) {
        // 分析章节下载进度
        if (!await fs.pathExists(chapterDir)) {
            return { existingFiles: [], missingPages: [], isComplete: false, maxPage: 0 };
        }

        const files = await fs.readdir(chapterDir);
        const imageFiles = files.filter(f => f.endsWith('.png') || f.endsWith('.jpg') || f.endsWith('.jpeg'));

        // 提取页面编号
        const pageNumbers = imageFiles.map(file => {
            const match = file.match(/^(\d+)-/);
            return match ? parseInt(match[1]) : 0;
        }).filter(num => num > 0).sort((a, b) => a - b);

        const maxPage = pageNumbers.length > 0 ? Math.max(...pageNumbers) : 0;
        const missingPages = [];

        // 检查连续性
        for (let i = 1; i <= maxPage; i++) {
            if (!pageNumbers.includes(i)) {
                missingPages.push(i);
            }
        }

        return {
            existingFiles: imageFiles,
            missingPages,
            isComplete: missingPages.length === 0 && maxPage > 0,
            maxPage,
            totalExisting: pageNumbers.length
        };
    }

    async checkChapterExists(mangaId, chapter) {
        // 检查章节是否存在（不下载，只检查）
        try {
            const chapterUrl = `https://www.colamanga.com/manga-${mangaId}/${chapter}.html`;
            const response = await this.page.goto(chapterUrl, {
                waitUntil: 'domcontentloaded',
                timeout: 30000
            });

            if (response.status() === 404) {
                return false;
            }

            // 检查是否有图片内容
            try {
                await this.page.waitForSelector('.mh_comicpic', { timeout: 5000 });
                return true;
            } catch {
                return false;
            }
        } catch (error) {
            if (error.message.includes('404') || error.message.includes('net::ERR_HTTP_RESPONSE_CODE_FAILURE')) {
                return false;
            }
            throw error;
        }
    }

    sanitizeFileName(fileName) {
        // 清理文件名，移除不合法字符
        return fileName.replace(/[<>:"/\\|?*：？]/g, '_').trim();
    }

    async downloadFromMangaList(mangaListFile, startIndex = 0, count = null, maxChapters = null) {
        const mangaList = await fs.readJson(mangaListFile);
        console.log(`📚 加载漫画列表，共 ${mangaList.length} 个漫画`);

        const endIndex = count ? Math.min(startIndex + count, mangaList.length) : mangaList.length;

        for (let i = startIndex; i < endIndex; i++) {
            const manga = mangaList[i];
            console.log(`\n📖 [${i + 1}/${mangaList.length}] 开始下载: ${manga.name}`);

            try {
                // 如果没有指定最大章节数，则持续下载直到没有更多章节
                let chapter = 1;
                while (true) {
                    console.log(`\n📚 下载章节 ${chapter}...`);

                    try {
                        const success = await this.downloadMangaContent(manga.id, manga.name, chapter);
                        if (!success) {
                            console.log(`📄 章节 ${chapter} 不存在，停止下载后续章节`);
                            break;
                        }
                    } catch (error) {
                        console.error(`❌ 下载章节 ${chapter} 失败:`, error.message);
                        // 连续失败3次则停止
                        if (error.message.includes('404')) {
                            console.log(`📄 章节 ${chapter} 不存在，停止下载`);
                            break;
                        }
                    }

                    chapter++;
                    
                    // 如果指定了最大章节数，检查是否达到限制
                    if (maxChapters && chapter > maxChapters) {
                        console.log(`📚 已达到最大章节数限制 ${maxChapters}，停止下载`);
                        break;
                    }

                    // 章节间延时
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            } catch (error) {
                console.error(`❌ 下载漫画 ${manga.name} 失败:`, error.message);
            }

            // 漫画间延时
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    async close() {
        if (this.context) {
            await this.context.close();
            console.log('🔒 浏览器已关闭');
        }
    }
}

// 测试新的下载逻辑
async function testNewDownloadLogic() {
    const downloader = new MangaContentDownloader();

    try {
        await downloader.init();

        // 测试单个章节下载
        console.log('🧪 测试新的下载逻辑...');
        const success = await downloader.downloadMangaContent('ap101511', '测试漫画', 1);

        if (success) {
            console.log('✅ 新下载逻辑测试成功！');
        } else {
            console.log('❌ 新下载逻辑测试失败！');
        }

    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await downloader.close();
    }
}

// 主函数
async function main() {
    const downloader = new MangaContentDownloader();

    try {
        await downloader.init();

        // 示例：下载单个漫画
        // await downloader.downloadMangaContent('ap101511', '示例漫画', 1);

        // 示例：从漫画列表文件批量下载
        const mangaListFile = path.join('./manga-ids.json');
        if (await fs.pathExists(mangaListFile)) {
            await downloader.downloadFromMangaList(mangaListFile, 0, 5, 3); // 下载前5个漫画，每个漫画最多3章
        } else {
            console.log('❌ 未找到漫画列表文件，请先运行 collect-manga-ids.js');
        }

    } catch (error) {
        console.error('❌ 下载过程中出错:', error);
    } finally {
        await downloader.close();
    }
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = MangaContentDownloader;




