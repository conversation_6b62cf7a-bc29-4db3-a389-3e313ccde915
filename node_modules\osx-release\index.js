'use strict';
var os = require('os');

var nameMap = {
	'15': 'El Capitan',
	'14': 'Yosemite',
	'13': 'Mavericks',
	'12': 'Mountain Lion',
	'11': '<PERSON>',
	'10': '<PERSON> Leopard',
	'9': '<PERSON><PERSON>',
	'8': '<PERSON>',
	'7': '<PERSON>',
	'6': 'Jaguar',
	'5': 'Puma'
};

module.exports = function (release) {
	release = (release || os.release()).split('.')[0];
	return {
		name: nameMap[release],
		version: '10.' + (Number(release) - 4)
	};
};
