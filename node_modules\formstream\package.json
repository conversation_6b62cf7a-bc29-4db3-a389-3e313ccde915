{"name": "formstream", "version": "1.5.1", "description": "A multipart/form-data encoded stream, helper for file upload.", "main": "lib/formstream.js", "types": "types/formstream.d.ts", "files": ["lib", "types/formstream.d.ts"], "scripts": {"test": "egg-bin test", "cov": "egg-bin cov", "ci": "npm run lint && npm run tsd && npm run cov && NODE_DEBUG=formstream npm run cov", "lint": "jshint .", "tsd": "tsd", "contributor": "git-contributor"}, "repository": {"type": "git", "url": "git://github.com/node-modules/formstream.git"}, "keywords": ["form", "stream", "multipart", "form-data", "upload", "postfile", "request"], "dependencies": {"destroy": "^1.0.4", "mime": "^2.5.2", "node-hex": "^1.0.1", "pause-stream": "~0.0.11"}, "devDependencies": {"@types/node": "^20.4.3", "connect-multiparty": "1", "egg-bin": "^5.6.1", "express": "^4.16.4", "git-contributor": "^2.1.5", "jshint": "^2.13.6", "pedding": "1", "should": "4", "tsd": "^0.28.1", "urllib": "2"}, "author": "fengmk2 <<EMAIL>> (https://github.com/fengmk2)", "license": "MIT"}